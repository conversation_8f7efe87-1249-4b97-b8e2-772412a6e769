// Declare Packer from binpackingjs
declare var Packer: any; // Use 'any' for simplicity with the non-typed global script

// --- Interfaces ---
interface BaseMaterial {
  id: string;
  name: string;
  length: number;
  width: number;
  unit: Unit;
  quantity: number;
  thickness?: string;
}

interface ProjectPiece {
  id: string;
  name: string;
  length: number;
  width: number;
  unit: Unit;
  quantity: number;
  grainDirection: GrainDirection;
  originalIndex?: number; // To map back to user's piece definition if needed
}

interface Project {
  id: string;
  name: string;
  pieces: ProjectPiece[];
  sawKerf: number;
  kerfUnit: Unit;
  createdAt: Date;
  updatedAt: Date;
}

interface PlacedPiece extends ProjectPiece {
  x: number;
  y: number;
  packedWidth: number; // Width used for packing (includes kerf)
  packedHeight: number; // Height used for packing (includes kerf)
  sheetId: string;
  color: string;
}

interface OptimizedSheetLayout {
  sheetId: string;
  baseMaterial: BaseMaterial; // The specific sheet instance used
  pieces: PlacedPiece[];
  widthUsed: number; // Actual width of the base material in base units
  heightUsed: number; // Actual height of the base material in base units
}

type Unit = "mm" | "cm" | "in";
type GrainDirection = "none" | "length" | "width";

// Type alias for pieces being prepared for packing
type PieceForPacking = ProjectPiece & {
  uniqueId: string;
  packed: boolean;
  baseUnitWidth: number;
  baseUnitHeight: number;
  packedWidth: number;
  packedHeight: number;
};

// Interface for blocks processed by the Packer library
interface PackerBlock {
  w: number; // width of the block
  h: number; // height of the block
  data: PieceForPacking; // The original piece data
  fit?: {
    // This property is added by the packer if the block is placed
    x: number;
    y: number;
  };
}

// --- State ---
let inventory: BaseMaterial[] = [];
let projects: Project[] = [];
let currentProjectId: string | null = null;
let projectPieces: ProjectPiece[] = [];
let projectName: string = "My Project";
let sawKerf: number = 3;
let kerfUnit: Unit = "mm";
const BASE_UNIT: Unit = "mm"; // Internal unit for calculations

let optimizedLayouts: OptimizedSheetLayout[] = [];
let currentSheetIndex = 0;

// --- localStorage Keys ---
const LOCAL_STORAGE_INVENTORY_KEY = "woodOptimizer.inventory";
const LOCAL_STORAGE_PROJECTS_KEY = "woodOptimizer.projects";
const LOCAL_STORAGE_CURRENT_PROJECT_KEY = "woodOptimizer.currentProject";
// Legacy keys for backward compatibility
const LOCAL_STORAGE_PROJECT_PIECES_KEY = "woodOptimizer.projectPieces";
const LOCAL_STORAGE_PROJECT_NAME_KEY = "woodOptimizer.projectName";
const LOCAL_STORAGE_SAW_KERF_KEY = "woodOptimizer.sawKerf";
const LOCAL_STORAGE_KERF_UNIT_KEY = "woodOptimizer.kerfUnit";

// --- UI Element References ---
// Project Management
const projectSelectEl = document.getElementById(
  "projectSelect",
) as HTMLSelectElement;
const newProjectNameEl = document.getElementById(
  "newProjectName",
) as HTMLInputElement;
const createProjectBtn = document.getElementById(
  "createProjectBtn",
) as HTMLButtonElement;
const deleteProjectBtn = document.getElementById(
  "deleteProjectBtn",
) as HTMLButtonElement;

// Inventory
const materialNameEl = document.getElementById(
  "materialName",
) as HTMLInputElement;
const materialLengthEl = document.getElementById(
  "materialLength",
) as HTMLInputElement;
const materialWidthEl = document.getElementById(
  "materialWidth",
) as HTMLInputElement;
const materialUnitEl = document.getElementById(
  "materialUnit",
) as HTMLSelectElement;
const materialQuantityEl = document.getElementById(
  "materialQuantity",
) as HTMLInputElement;
const materialThicknessEl = document.getElementById(
  "materialThickness",
) as HTMLInputElement;
const addMaterialBtn = document.getElementById(
  "addMaterialBtn",
) as HTMLButtonElement;
const inventoryListEl = document.getElementById(
  "inventoryList",
) as HTMLUListElement;

// Project Pieces
const projectNameEl = document.getElementById(
  "projectName",
) as HTMLInputElement;
const pieceNameEl = document.getElementById("pieceName") as HTMLInputElement;
const pieceLengthEl = document.getElementById(
  "pieceLength",
) as HTMLInputElement;
const pieceWidthEl = document.getElementById("pieceWidth") as HTMLInputElement;
const pieceUnitEl = document.getElementById("pieceUnit") as HTMLSelectElement;
const pieceQuantityEl = document.getElementById(
  "pieceQuantity",
) as HTMLInputElement;
const grainDirectionEl = document.getElementById(
  "grainDirection",
) as HTMLSelectElement;
const addPieceBtn = document.getElementById("addPieceBtn") as HTMLButtonElement;
const projectPiecesListEl = document.getElementById(
  "projectPiecesList",
) as HTMLUListElement;

// Settings
const sawKerfEl = document.getElementById("sawKerf") as HTMLInputElement;
const kerfUnitEl = document.getElementById("kerfUnit") as HTMLSelectElement;

// Optimization & Visualization
const optimizeBtn = document.getElementById("optimizeBtn") as HTMLButtonElement;
const visualizationCanvas = document.getElementById(
  "visualizationCanvas",
) as HTMLCanvasElement;
const optimizationMessageEl = document.getElementById(
  "optimizationMessage",
) as HTMLParagraphElement;

// Sheet Navigation
const prevSheetBtn = document.getElementById(
  "prevSheetBtn",
) as HTMLButtonElement;
const nextSheetBtn = document.getElementById(
  "nextSheetBtn",
) as HTMLButtonElement;
const sheetIndicatorEl = document.getElementById(
  "sheetIndicator",
) as HTMLSpanElement;

// Results
const cuttingListOutputEl = document.getElementById(
  "cuttingListOutput",
) as HTMLTextAreaElement;
const exportBtn = document.getElementById("exportBtn") as HTMLButtonElement;
const printBtn = document.getElementById("printBtn") as HTMLButtonElement;

// --- Utility Functions ---
function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

function convertToUnit(value: number, fromUnit: Unit, toUnit: Unit): number {
  if (fromUnit === toUnit) return value;

  const factors: Record<Unit, number> = { mm: 1, cm: 10, in: 25.4 };
  const valueInMm = value * factors[fromUnit];
  return valueInMm / factors[toUnit];
}

const PIECE_COLORS = [
  "#e74c3c",
  "#3498db",
  "#2ecc71",
  "#f1c40f",
  "#9b59b6",
  "#1abc9c",
  "#e67e22",
  "#d35400",
  "#c0392b",
  "#2980b9",
];
function getPieceColor(index: number): string {
  return PIECE_COLORS[index % PIECE_COLORS.length];
}

// --- localStorage Functions ---
function saveState() {
  try {
    // Save inventory
    localStorage.setItem(
      LOCAL_STORAGE_INVENTORY_KEY,
      JSON.stringify(inventory),
    );

    // Save projects
    localStorage.setItem(
      LOCAL_STORAGE_PROJECTS_KEY,
      JSON.stringify(projects),
    );

    // Save current project ID
    localStorage.setItem(
      LOCAL_STORAGE_CURRENT_PROJECT_KEY,
      currentProjectId || "",
    );

    // Update current project data if one is selected
    if (currentProjectId) {
      updateCurrentProject();
    }
  } catch (e) {
    console.error("Error saving state to localStorage:", e);
    alert(
      "Could not save your data. Your browser's localStorage might be full or disabled.",
    );
  }
}

function loadState() {
  try {
    // Load inventory
    const storedInventory = localStorage.getItem(LOCAL_STORAGE_INVENTORY_KEY);
    if (storedInventory) {
      inventory = JSON.parse(storedInventory);
    }

    // Load projects
    const storedProjects = localStorage.getItem(LOCAL_STORAGE_PROJECTS_KEY);
    if (storedProjects) {
      projects = JSON.parse(storedProjects).map((p: any) => ({
        ...p,
        createdAt: new Date(p.createdAt),
        updatedAt: new Date(p.updatedAt),
      }));
    }

    // Load current project ID
    const storedCurrentProject = localStorage.getItem(LOCAL_STORAGE_CURRENT_PROJECT_KEY);
    if (storedCurrentProject) {
      currentProjectId = storedCurrentProject || null;
    }

    // Backward compatibility: migrate old single project data
    if (projects.length === 0) {
      migrateOldProjectData();
    }

    // Load current project or create default
    if (currentProjectId && projects.find(p => p.id === currentProjectId)) {
      loadProject(currentProjectId);
    } else if (projects.length > 0) {
      loadProject(projects[0].id);
    } else {
      createDefaultProject();
    }
  } catch (e) {
    console.error("Error loading state from localStorage:", e);
    // Don't alert here, just proceed with defaults
    createDefaultProject();
  }
}

// --- Project Management Functions ---

function createDefaultProject() {
  const defaultProject: Project = {
    id: generateId(),
    name: "My Project",
    pieces: [],
    sawKerf: 3,
    kerfUnit: "mm",
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  projects = [defaultProject];
  currentProjectId = defaultProject.id;
  loadProject(defaultProject.id);
  saveState();
}

function migrateOldProjectData() {
  // Check for old project data and migrate it
  const oldPieces = localStorage.getItem(LOCAL_STORAGE_PROJECT_PIECES_KEY);
  const oldName = localStorage.getItem(LOCAL_STORAGE_PROJECT_NAME_KEY);
  const oldKerf = localStorage.getItem(LOCAL_STORAGE_SAW_KERF_KEY);
  const oldKerfUnit = localStorage.getItem(LOCAL_STORAGE_KERF_UNIT_KEY);

  if (oldPieces || oldName) {
    const migratedProject: Project = {
      id: generateId(),
      name: oldName || "My Project",
      pieces: oldPieces ? JSON.parse(oldPieces) : [],
      sawKerf: oldKerf ? parseFloat(oldKerf) : 3,
      kerfUnit: (oldKerfUnit as Unit) || "mm",
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    projects = [migratedProject];
    currentProjectId = migratedProject.id;

    // Clean up old keys
    localStorage.removeItem(LOCAL_STORAGE_PROJECT_PIECES_KEY);
    localStorage.removeItem(LOCAL_STORAGE_PROJECT_NAME_KEY);
    localStorage.removeItem(LOCAL_STORAGE_SAW_KERF_KEY);
    localStorage.removeItem(LOCAL_STORAGE_KERF_UNIT_KEY);
  }
}

function updateCurrentProject() {
  if (!currentProjectId) return;

  const projectIndex = projects.findIndex(p => p.id === currentProjectId);
  if (projectIndex !== -1) {
    projects[projectIndex] = {
      ...projects[projectIndex],
      name: projectName,
      pieces: [...projectPieces],
      sawKerf,
      kerfUnit,
      updatedAt: new Date(),
    };
  }
}

function loadProject(projectId: string) {
  const project = projects.find(p => p.id === projectId);
  if (!project) return;

  currentProjectId = projectId;
  projectName = project.name;
  projectPieces = [...project.pieces];
  sawKerf = project.sawKerf;
  kerfUnit = project.kerfUnit;

  // Update UI elements
  projectNameEl.value = projectName;
  sawKerfEl.value = sawKerf.toString();
  kerfUnitEl.value = kerfUnit;

  // Clear optimization results when switching projects
  optimizedLayouts = [];
  currentSheetIndex = 0;

  renderProjectPiecesList();
  renderProjectSelect();
  updateSheetNavigation();
  renderVisualization();
}

function createNewProject() {
  const name = newProjectNameEl.value.trim();
  if (!name) {
    alert("Please enter a project name.");
    return;
  }

  if (projects.some(p => p.name === name)) {
    alert("A project with this name already exists.");
    return;
  }

  const newProject: Project = {
    id: generateId(),
    name,
    pieces: [],
    sawKerf: 3,
    kerfUnit: "mm",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  projects.push(newProject);
  loadProject(newProject.id);
  newProjectNameEl.value = "";
  saveState();
}

function deleteCurrentProject() {
  if (!currentProjectId || projects.length <= 1) {
    alert("Cannot delete the last project.");
    return;
  }

  const project = projects.find(p => p.id === currentProjectId);
  if (!project) return;

  if (!confirm(`Are you sure you want to delete the project "${project.name}"? This action cannot be undone.`)) {
    return;
  }

  projects = projects.filter(p => p.id !== currentProjectId);
  loadProject(projects[0].id);
  saveState();
}

function renderProjectSelect() {
  projectSelectEl.innerHTML = "";
  projects.forEach(project => {
    const option = document.createElement("option");
    option.value = project.id;
    option.textContent = project.name;
    option.selected = project.id === currentProjectId;
    projectSelectEl.appendChild(option);
  });
}

// --- DOM Update Functions ---
function renderInventoryList() {
  inventoryListEl.innerHTML = "";
  inventory.forEach((item) => {
    const li = document.createElement("li");
    li.setAttribute(
      "aria-label",
      `Inventory item: ${item.quantity}x ${item.name} ${item.length}${item.unit} x ${item.width}${item.unit}`,
    );
    li.innerHTML = `
            <span>${item.quantity}x ${item.name} (${item.length}${item.unit} x ${item.width}${item.unit}${item.thickness ? ", " + item.thickness : ""})</span>
            <div class="item-buttons">
                <button data-id="${item.id}" class="edit-material-btn" aria-label="Edit ${item.name}">Edit</button>
                <button data-id="${item.id}" class="remove-material-btn" aria-label="Remove ${item.name} from inventory">Remove</button>
            </div>
        `;
    inventoryListEl.appendChild(li);
  });
  addButtonListeners(
    inventoryListEl,
    "edit-material-btn",
    editBaseMaterial,
  );
  addButtonListeners(
    inventoryListEl,
    "remove-material-btn",
    removeBaseMaterial,
  );
}

function renderProjectPiecesList() {
  projectPiecesListEl.innerHTML = "";
  projectPieces.forEach((piece) => {
    const li = document.createElement("li");
    li.setAttribute(
      "aria-label",
      `Project piece: ${piece.quantity}x ${piece.name} ${piece.length}${piece.unit} x ${piece.width}${piece.unit}`,
    );
    li.innerHTML = `
            <span>${piece.quantity}x ${piece.name} (${piece.length}${piece.unit} x ${piece.width}${piece.unit}, Grain: ${piece.grainDirection})</span>
            <div class="item-buttons">
                <button data-id="${piece.id}" class="edit-piece-btn" aria-label="Edit ${piece.name}">Edit</button>
                <button data-id="${piece.id}" class="remove-piece-btn" aria-label="Remove ${piece.name} from project">Remove</button>
            </div>
        `;
    projectPiecesListEl.appendChild(li);
  });
  addButtonListeners(
    projectPiecesListEl,
    "edit-piece-btn",
    editProjectPiece,
  );
  addButtonListeners(
    projectPiecesListEl,
    "remove-piece-btn",
    removeProjectPiece,
  );
}

function addButtonListeners(
  listEl: HTMLElement,
  btnClass: string,
  clickFn: (id: string) => void,
) {
  listEl.querySelectorAll(`.${btnClass}`).forEach((button) => {
    button.addEventListener("click", (e) => {
      const target = e.target as HTMLButtonElement;
      const id = target.dataset.id;
      if (id) clickFn(id);
    });
  });
}

// --- Event Handlers & Logic ---
function addBaseMaterial() {
  const name =
    materialNameEl.value.trim() || `Material ${inventory.length + 1}`;
  const length = parseFloat(materialLengthEl.value);
  const width = parseFloat(materialWidthEl.value);
  const unit = materialUnitEl.value as Unit;
  const quantity = parseInt(materialQuantityEl.value, 10);
  const thickness = materialThicknessEl.value.trim();

  if (
    isNaN(length) ||
    length <= 0 ||
    isNaN(width) ||
    width <= 0 ||
    isNaN(quantity) ||
    quantity <= 0
  ) {
    alert("Please enter valid dimensions and quantity for the material.");
    return;
  }

  inventory.push({
    id: generateId(),
    name,
    length,
    width,
    unit,
    quantity,
    thickness,
  });
  renderInventoryList();
  saveState();
  materialNameEl.value = "";
  materialLengthEl.value = "";
  materialWidthEl.value = "";
  materialQuantityEl.value = "1";
  materialThicknessEl.value = "";
}

function removeBaseMaterial(id: string) {
  inventory = inventory.filter((item) => item.id !== id);
  renderInventoryList();
  saveState();
}

function editBaseMaterial(id: string) {
  const material = inventory.find((item) => item.id === id);
  if (!material) return;

  // Populate form with existing values
  materialNameEl.value = material.name;
  materialLengthEl.value = material.length.toString();
  materialWidthEl.value = material.width.toString();
  materialUnitEl.value = material.unit;
  materialQuantityEl.value = material.quantity.toString();
  materialThicknessEl.value = material.thickness || "";

  // Change button text and function
  addMaterialBtn.textContent = "Update Material";
  addMaterialBtn.onclick = () => updateBaseMaterial(id);

  // Scroll to form
  materialNameEl.scrollIntoView({ behavior: "smooth" });
  materialNameEl.focus();
}

function updateBaseMaterial(id: string) {
  const name = materialNameEl.value.trim() || `Material ${inventory.length + 1}`;
  const length = parseFloat(materialLengthEl.value);
  const width = parseFloat(materialWidthEl.value);
  const unit = materialUnitEl.value as Unit;
  const quantity = parseInt(materialQuantityEl.value, 10);
  const thickness = materialThicknessEl.value.trim();

  if (
    isNaN(length) ||
    length <= 0 ||
    isNaN(width) ||
    width <= 0 ||
    isNaN(quantity) ||
    quantity <= 0
  ) {
    alert("Please enter valid dimensions and quantity for the material.");
    return;
  }

  const materialIndex = inventory.findIndex((item) => item.id === id);
  if (materialIndex !== -1) {
    inventory[materialIndex] = {
      ...inventory[materialIndex],
      name,
      length,
      width,
      unit,
      quantity,
      thickness,
    };
    renderInventoryList();
    saveState();
    resetMaterialForm();
  }
}

function resetMaterialForm() {
  materialNameEl.value = "";
  materialLengthEl.value = "";
  materialWidthEl.value = "";
  materialQuantityEl.value = "1";
  materialThicknessEl.value = "";
  addMaterialBtn.textContent = "Add Material";
  addMaterialBtn.onclick = addBaseMaterial;
}

function addProjectPiece() {
  const name = pieceNameEl.value.trim() || `Piece ${projectPieces.length + 1}`;
  const length = parseFloat(pieceLengthEl.value);
  const width = parseFloat(pieceWidthEl.value);
  const unit = pieceUnitEl.value as Unit;
  const quantity = parseInt(pieceQuantityEl.value, 10);
  const grain = grainDirectionEl.value as GrainDirection;

  if (
    isNaN(length) ||
    length <= 0 ||
    isNaN(width) ||
    width <= 0 ||
    isNaN(quantity) ||
    quantity <= 0
  ) {
    alert("Please enter valid dimensions and quantity for the piece.");
    return;
  }

  projectPieces.push({
    id: generateId(),
    name,
    length,
    width,
    unit,
    quantity,
    grainDirection: grain,
    originalIndex: projectPieces.length,
  });
  renderProjectPiecesList();
  saveState();
  pieceNameEl.value = "";
  pieceLengthEl.value = "";
  pieceWidthEl.value = "";
  pieceQuantityEl.value = "1";
}

function removeProjectPiece(id: string) {
  projectPieces = projectPieces.filter((piece) => piece.id !== id);
  renderProjectPiecesList();
  saveState();
}

function editProjectPiece(id: string) {
  const piece = projectPieces.find((p) => p.id === id);
  if (!piece) return;

  // Populate form with existing values
  pieceNameEl.value = piece.name;
  pieceLengthEl.value = piece.length.toString();
  pieceWidthEl.value = piece.width.toString();
  pieceUnitEl.value = piece.unit;
  pieceQuantityEl.value = piece.quantity.toString();
  grainDirectionEl.value = piece.grainDirection;

  // Change button text and function
  addPieceBtn.textContent = "Update Piece";
  addPieceBtn.onclick = () => updateProjectPiece(id);

  // Scroll to form
  pieceNameEl.scrollIntoView({ behavior: "smooth" });
  pieceNameEl.focus();
}

function updateProjectPiece(id: string) {
  const name = pieceNameEl.value.trim() || `Piece ${projectPieces.length + 1}`;
  const length = parseFloat(pieceLengthEl.value);
  const width = parseFloat(pieceWidthEl.value);
  const unit = pieceUnitEl.value as Unit;
  const quantity = parseInt(pieceQuantityEl.value, 10);
  const grain = grainDirectionEl.value as GrainDirection;

  if (
    isNaN(length) ||
    length <= 0 ||
    isNaN(width) ||
    width <= 0 ||
    isNaN(quantity) ||
    quantity <= 0
  ) {
    alert("Please enter valid dimensions and quantity for the piece.");
    return;
  }

  const pieceIndex = projectPieces.findIndex((p) => p.id === id);
  if (pieceIndex !== -1) {
    projectPieces[pieceIndex] = {
      ...projectPieces[pieceIndex],
      name,
      length,
      width,
      unit,
      quantity,
      grainDirection: grain,
    };
    renderProjectPiecesList();
    saveState();
    resetPieceForm();
  }
}

function resetPieceForm() {
  pieceNameEl.value = "";
  pieceLengthEl.value = "";
  pieceWidthEl.value = "";
  pieceQuantityEl.value = "1";
  addPieceBtn.textContent = "Add Piece";
  addPieceBtn.onclick = addProjectPiece;
}

function updateSettings() {
  projectName = projectNameEl.value.trim() || "My Project";
  sawKerf = parseFloat(sawKerfEl.value);
  kerfUnit = kerfUnitEl.value as Unit;
  if (isNaN(sawKerf) || sawKerf < 0) {
    alert("Saw kerf must be a non-negative number.");
    sawKerf = 0; // Default to 0 if invalid
    sawKerfEl.value = "0";
  }
  saveState();
}

function handleOptimize() {
  updateSettings(); // This will also call saveState
  optimizationMessageEl.textContent = "Optimizing...";
  optimizedLayouts = [];
  currentSheetIndex = 0;

  // Check if Packer library is loaded
  if (typeof Packer === "undefined") {
    console.error(
      "Packer library is not loaded or defined. Make sure binpacking.js is included and loaded correctly from the CDN.",
    );
    const errorMessage =
      "Error: Core packing library (Packer.js) not found. Please check your internet connection, refresh the page, or ensure browser extensions are not blocking scripts.";
    optimizationMessageEl.textContent = errorMessage;
    alert(errorMessage);
    return;
  }

  // Debug: Log material and piece dimensions
  console.log("=== OPTIMIZATION DEBUG INFO ===");
  console.log("Materials:", inventory.map(m => ({
    name: m.name,
    width: m.width,
    length: m.length,
    unit: m.unit,
    widthInMM: convertToUnit(m.width, m.unit, BASE_UNIT),
    lengthInMM: convertToUnit(m.length, m.unit, BASE_UNIT)
  })));
  console.log("Pieces:", projectPieces.map(p => ({
    name: p.name,
    width: p.width,
    length: p.length,
    unit: p.unit,
    widthInMM: convertToUnit(p.width, p.unit, BASE_UNIT),
    lengthInMM: convertToUnit(p.length, p.unit, BASE_UNIT)
  })));
  console.log("Saw Kerf:", sawKerf, kerfUnit, "->", convertToUnit(sawKerf, kerfUnit, BASE_UNIT), BASE_UNIT);

  if (inventory.length === 0) {
    optimizationMessageEl.textContent =
      "Error: Please add base materials to your inventory.";
    alert("Please add base materials to your inventory.");
    return;
  }
  if (projectPieces.length === 0) {
    optimizationMessageEl.textContent =
      "Error: Please add pieces to your project.";
    alert("Please add pieces to your project.");
    return;
  }

  // Validate dimensions - check for pieces larger than any available material
  const kerfInBaseUnit = convertToUnit(sawKerf, kerfUnit, BASE_UNIT);
  let hasOversizedPieces = false;
  let oversizedPieceNames: string[] = [];

  for (const piece of projectPieces) {
    const pieceWidthInBase = convertToUnit(piece.width, piece.unit, BASE_UNIT);
    const pieceLengthInBase = convertToUnit(piece.length, piece.unit, BASE_UNIT);
    const packedPieceWidth = pieceWidthInBase + kerfInBaseUnit;
    const packedPieceLength = pieceLengthInBase + kerfInBaseUnit;

    let canFitInAnyMaterial = false;
    for (const material of inventory) {
      const materialWidthInBase = convertToUnit(material.width, material.unit, BASE_UNIT);
      const materialLengthInBase = convertToUnit(material.length, material.unit, BASE_UNIT);

      // Check if piece fits in either orientation
      if ((packedPieceWidth <= materialWidthInBase && packedPieceLength <= materialLengthInBase) ||
          (packedPieceLength <= materialWidthInBase && packedPieceWidth <= materialLengthInBase)) {
        canFitInAnyMaterial = true;
        break;
      }
    }

    if (!canFitInAnyMaterial) {
      hasOversizedPieces = true;
      oversizedPieceNames.push(`"${piece.name}" (${piece.length}${piece.unit} x ${piece.width}${piece.unit})`);
    }
  }

  if (hasOversizedPieces) {
    optimizationMessageEl.textContent =
      `Error: Some pieces are too large for any available material: ${oversizedPieceNames.join(', ')}. Consider using larger materials or reducing piece sizes.`;
    return;
  }

  let allPiecesToPack: PieceForPacking[] = [];
  projectPieces.forEach((p, originalIdx) => {
    for (let i = 0; i < p.quantity; i++) {
      // Map piece dimensions to packing coordinates
      // Piece width becomes the X-axis (horizontal) dimension
      // Piece length becomes the Y-axis (vertical) dimension
      const baseUnitW = convertToUnit(p.width, p.unit, BASE_UNIT);
      const baseUnitL = convertToUnit(p.length, p.unit, BASE_UNIT);
      allPiecesToPack.push({
        ...p,
        uniqueId: `${p.id}-${i}`,
        packed: false,
        baseUnitWidth: baseUnitW,
        baseUnitHeight: baseUnitL,
        packedWidth: baseUnitW + kerfInBaseUnit,
        packedHeight: baseUnitL + kerfInBaseUnit,
        originalIndex: originalIdx,
      });
    }
  });

  allPiecesToPack.sort((a, b) => {
    const maxDimA = Math.max(a.packedWidth, a.packedHeight);
    const maxDimB = Math.max(b.packedWidth, b.packedHeight);
    if (maxDimA !== maxDimB) return maxDimB - maxDimA;
    return b.packedWidth * b.packedHeight - a.packedWidth * a.packedHeight;
  });

  for (const materialTemplate of inventory) {
    for (let i = 0; i < materialTemplate.quantity; i++) {
      if (allPiecesToPack.every((p) => p.packed)) break;

      const sheetId = `sheet-${materialTemplate.id}-${i}`;
      const materialInstance: BaseMaterial = {
        ...materialTemplate,
        id: sheetId,
        quantity: 1,
      };

      // Map material dimensions to packing coordinates
      // Material width becomes the X-axis (horizontal) dimension
      // Material length becomes the Y-axis (vertical) dimension
      const binWidth = convertToUnit(
        materialInstance.width,
        materialInstance.unit,
        BASE_UNIT,
      );
      const binHeight = convertToUnit(
        materialInstance.length,
        materialInstance.unit,
        BASE_UNIT,
      );

      const packer = new Packer(binWidth, binHeight); // This is where Packer is used

      const blocksForThisSheet: PackerBlock[] = allPiecesToPack
        .filter((p) => !p.packed)
        .map((p) => ({
          w: p.packedWidth,
          h: p.packedHeight,
          data: p,
        }));

      if (blocksForThisSheet.length === 0) continue;

      // Debug: Log packing attempt
      console.log(`Packing attempt for material: ${materialInstance.name}`);
      console.log(`  Bin dimensions: ${binWidth}mm x ${binHeight}mm`);
      console.log(`  Pieces to pack: ${blocksForThisSheet.length}`);
      console.log(`  Piece dimensions:`, blocksForThisSheet.map(b => ({
        name: b.data.name,
        w: b.w,
        h: b.h,
        originalW: b.data.width,
        originalH: b.data.length,
        unit: b.data.unit
      })));

      packer.fit(blocksForThisSheet);

      const placedPiecesOnThisSheet: PlacedPiece[] = [];
      blocksForThisSheet.forEach((block: PackerBlock) => {
        if (block.fit) {
          const originalPieceData = block.data;
          originalPieceData.packed = true;
          placedPiecesOnThisSheet.push({
            ...(originalPieceData as ProjectPiece),
            id: originalPieceData.uniqueId,
            x: block.fit.x,
            y: block.fit.y,
            packedWidth: block.w,
            packedHeight: block.h,
            sheetId: materialInstance.id,
            color: getPieceColor(originalPieceData.originalIndex!),
          });
        }
      });

      if (placedPiecesOnThisSheet.length > 0) {
        optimizedLayouts.push({
          sheetId: materialInstance.id,
          baseMaterial: materialInstance,
          pieces: placedPiecesOnThisSheet,
          widthUsed: binWidth,
          heightUsed: binHeight,
        });
      }
    }
    if (allPiecesToPack.every((p) => p.packed)) break;
  }

  if (optimizedLayouts.length > 0) {
    optimizationMessageEl.textContent = `Optimization complete! ${optimizedLayouts.length} sheet(s) used.`;
    renderVisualization();
    renderCuttingList();
  } else if (
    allPiecesToPack.length > 0 &&
    !allPiecesToPack.some((p) => p.packed)
  ) {
    optimizationMessageEl.textContent =
      "Optimization failed: No pieces could be packed. Check dimensions, kerf, and available material stock.";
  } else if (allPiecesToPack.some((p) => !p.packed)) {
    optimizationMessageEl.textContent = `Optimization partially complete. Some pieces could not be packed. ${optimizedLayouts.length} sheet(s) used.`;
    renderVisualization();
    renderCuttingList();
  } else {
    optimizationMessageEl.textContent =
      "No pieces to pack or no materials available.";
  }
  updateSheetNavigation();
}

// --- Visualization ---
function renderVisualization() {
  if (optimizedLayouts.length === 0) {
    const ctx = visualizationCanvas.getContext("2d");
    if (!ctx) return;
    ctx.clearRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
    const canvasWidth = visualizationCanvas.clientWidth || 300;
    visualizationCanvas.width = canvasWidth;
    visualizationCanvas.height = canvasWidth * (9 / 16);
    ctx.font = "14px Arial";
    ctx.textAlign = "center";
    ctx.fillStyle = "#777";
    ctx.fillText(
      "No layout to display. Add items and optimize.",
      canvasWidth / 2,
      visualizationCanvas.height / 2,
    );
    sheetIndicatorEl.textContent = "Sheet 0 of 0";
    return;
  }

  const layout = optimizedLayouts[currentSheetIndex];
  if (!layout) return;

  const ctx = visualizationCanvas.getContext("2d");
  if (!ctx) return;

  const canvasClientWidth = visualizationCanvas.clientWidth;
  const sheetAspectRatio = layout.heightUsed / layout.widthUsed;
  visualizationCanvas.width = canvasClientWidth;
  visualizationCanvas.height = canvasClientWidth * sheetAspectRatio;

  const scaleX = visualizationCanvas.width / layout.widthUsed;
  const scaleY = visualizationCanvas.height / layout.heightUsed;
  const scale = Math.min(scaleX, scaleY) * 0.98;

  const drawingWidth = layout.widthUsed * scale;
  const drawingHeight = layout.heightUsed * scale;
  const offsetX = (visualizationCanvas.width - drawingWidth) / 2;
  const offsetY = (visualizationCanvas.height - drawingHeight) / 2;

  ctx.clearRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);

  ctx.strokeStyle = "#333";
  ctx.lineWidth = 2;
  ctx.strokeRect(offsetX, offsetY, drawingWidth, drawingHeight);

  // Add dimension labels to the material sheet
  ctx.fillStyle = "#555";
  ctx.font = "10px Arial";
  ctx.textAlign = "center";

  // Top label: Material Length (vertical dimension in the layout)
  ctx.fillText(
    `Length: ${layout.baseMaterial.length}${layout.baseMaterial.unit} (${layout.heightUsed.toFixed(0)}${BASE_UNIT})`,
    offsetX + drawingWidth / 2,
    offsetY - 5 > 10 ? offsetY - 5 : 12,
  );

  // Side label: Material Width (horizontal dimension in the layout)
  ctx.save();
  ctx.translate(
    offsetX - 5 < 0 ? 12 : offsetX - 5,
    offsetY + drawingHeight / 2,
  );
  ctx.rotate(-Math.PI / 2);
  ctx.textAlign = "center";
  ctx.fillText(
    `Width: ${layout.baseMaterial.width}${layout.baseMaterial.unit} (${layout.widthUsed.toFixed(0)}${BASE_UNIT})`,
    0,
    0,
  );
  ctx.restore();

  layout.pieces.forEach((piece) => {
    const x = piece.x * scale + offsetX;
    const y = piece.y * scale + offsetY;
    const pieceActualWidth =
      convertToUnit(piece.width, piece.unit, BASE_UNIT) * scale;
    const pieceActualHeight =
      convertToUnit(piece.length, piece.unit, BASE_UNIT) * scale;

    const packedPieceWidth = piece.packedWidth * scale;
    const packedPieceHeight = piece.packedHeight * scale;

    ctx.fillStyle = piece.color + "60";
    ctx.fillRect(x, y, packedPieceWidth, packedPieceHeight);

    ctx.strokeStyle = piece.color;
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, packedPieceWidth, packedPieceHeight);

    const pieceXDisplayOffset = (packedPieceWidth - pieceActualWidth) / 2;
    const pieceYDisplayOffset = (packedPieceHeight - pieceActualHeight) / 2;

    ctx.fillStyle = piece.color;
    ctx.fillRect(
      x + pieceXDisplayOffset,
      y + pieceYDisplayOffset,
      pieceActualWidth,
      pieceActualHeight,
    );

    ctx.fillStyle = "#fff";
    ctx.font = "bold 10px Arial";
    ctx.textAlign = "center";
    const textX = x + pieceXDisplayOffset + pieceActualWidth / 2;
    const textY = y + pieceYDisplayOffset + pieceActualHeight / 2;

    const pieceLabel = `${piece.name}`;
    const pieceDims = `${piece.length}${piece.unit} x ${piece.width}${piece.unit}`;

    if (pieceActualHeight > 25 && pieceActualWidth > 50) {
      ctx.fillText(pieceLabel, textX, textY - 5);
      ctx.fillText(pieceDims, textX, textY + 8);
    } else if (pieceActualHeight > 10 && pieceActualWidth > 30) {
      ctx.fillText(pieceLabel, textX, textY);
    } else if (pieceActualHeight > 10 && pieceActualWidth > 10) {
      ctx.font = "bold 8px Arial";
      ctx.fillText(pieceLabel, textX, textY);
    }
  });
  updateSheetNavigation();
}

function updateSheetNavigation() {
  sheetIndicatorEl.textContent = `Sheet ${optimizedLayouts.length > 0 ? currentSheetIndex + 1 : 0} of ${optimizedLayouts.length}`;
  prevSheetBtn.disabled = currentSheetIndex === 0;
  nextSheetBtn.disabled = currentSheetIndex >= optimizedLayouts.length - 1;
}

// --- Cutting List & Export ---
function renderCuttingList() {
  if (optimizedLayouts.length === 0 && projectPieces.length === 0) {
    cuttingListOutputEl.value =
      "No project data or optimization performed yet.";
    return;
  }
  if (optimizedLayouts.length === 0 && projectPieces.length > 0) {
    cuttingListOutputEl.value =
      "Optimization has not been run or no pieces could be packed. Please check inputs and run optimizer.";
    return;
  }
  let output = `Project: ${projectName}\n`;
  output += `Saw Kerf: ${sawKerf}${kerfUnit}\n\n`;

  optimizedLayouts.forEach((layout, index) => {
    output += `--- SHEET ${index + 1} (${layout.baseMaterial.name}: ${layout.baseMaterial.length}${layout.baseMaterial.unit} x ${layout.baseMaterial.width}${layout.baseMaterial.unit}) ---\n`;
    if (layout.pieces.length === 0) {
      output += "  No pieces on this sheet.\n";
    } else {
      layout.pieces
        .sort((a, b) => a.y - b.y || a.x - b.x)
        .forEach((piece) => {
          output += `  - Piece: "${piece.name}" (Original: ${piece.length}${piece.unit} L x ${piece.width}${piece.unit} W)\n`;
          output += `    Position (top-left of packed area, ${BASE_UNIT}): X=${piece.x.toFixed(1)}, Y=${piece.y.toFixed(1)}\n`;
          output += `    Packed Area Size (${BASE_UNIT}, piece + kerf allowance): ${piece.packedWidth.toFixed(1)} W x ${piece.packedHeight.toFixed(1)} L\n\n`;
        });
    }
    output += "\n";
  });

  const allPackedPieceInstances: Record<string, number> = {};
  optimizedLayouts.forEach((layout) => {
    layout.pieces.forEach((lp) => {
      const key = `${lp.originalIndex}`;
      allPackedPieceInstances[key] = (allPackedPieceInstances[key] || 0) + 1;
    });
  });

  let hasUnpacked = false;
  let unpackedOutput = "--- UNPACKED PIECES ---\n";
  projectPieces.forEach((p, originalIdx) => {
    const packedCount = allPackedPieceInstances[`${originalIdx}`] || 0;
    const remainingQty = p.quantity - packedCount;
    if (remainingQty > 0) {
      unpackedOutput += `  - ${remainingQty}x "${p.name}" (${p.length}${p.unit} x ${p.width}${p.unit})\n`;
      hasUnpacked = true;
    }
  });

  if (hasUnpacked) {
    output += unpackedOutput;
  } else if (projectPieces.length > 0 && optimizedLayouts.length > 0) {
    output += "--- All project pieces successfully packed. ---\n";
  }

  cuttingListOutputEl.value = output;
}

function handleExport() {
  if (
    !cuttingListOutputEl.value ||
    cuttingListOutputEl.value.startsWith("No project data") ||
    cuttingListOutputEl.value.startsWith("Optimization has not been run")
  ) {
    alert("No cutting list to export. Please add data and optimize first.");
    return;
  }
  const blob = new Blob([cuttingListOutputEl.value], { type: "text/plain" });
  const anchor = document.createElement("a");
  anchor.download = `${projectName.replace(/\s+/g, "_")}_cut_list.txt`;
  anchor.href = URL.createObjectURL(blob);
  anchor.click();
  URL.revokeObjectURL(anchor.href);
}

function handlePrint() {
  if (
    !cuttingListOutputEl.value ||
    cuttingListOutputEl.value.startsWith("No project data") ||
    cuttingListOutputEl.value.startsWith("Optimization has not been run")
  ) {
    alert("No cutting list to print. Please add data and optimize first.");
    return;
  }
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write("<html><head><title>Cutting Plan Print</title>");
    printWindow.document.write(
      "<style>body{font-family:monospace;} pre{white-space:pre-wrap;} img{max-width:90vw; border:1px solid #ccc; margin-top:20px; page-break-inside: avoid;} h1,h2{font-family:sans-serif;}</style>",
    );
    printWindow.document.write("</head><body>");
    printWindow.document.write(`<h1>${projectName} - Cutting Plan</h1>`);
    printWindow.document.write("<h2>Cutting List:</h2>");
    printWindow.document.write(`<pre>${cuttingListOutputEl.value}</pre>`);
    if (optimizedLayouts.length > 0 && visualizationCanvas) {
      try {
        printWindow.document.write(
          "<h2>Layout Visualization (Current Sheet):</h2>",
        );
        printWindow.document.write(
          `<img src="${visualizationCanvas.toDataURL("image/png")}" alt="Cutting Layout Visualization"/>`,
        );
      } catch (e) {
        console.error("Error converting canvas to Data URL for printing:", e);
        printWindow.document.write(
          "<p>Could not generate image of current layout visualization.</p>",
        );
      }
    }
    printWindow.document.write("</body></html>");
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
    }, 500);
  } else {
    alert(
      "Could not open print window. Please check your browser's pop-up settings.",
    );
  }
}

// --- Initialize ---
document.addEventListener("DOMContentLoaded", () => {
  console.log("Initializing Wood Optimizer with enhanced features...");
  loadState();

  // Project management event listeners
  projectSelectEl.addEventListener("change", (e) => {
    const target = e.target as HTMLSelectElement;
    if (target.value && target.value !== currentProjectId) {
      loadProject(target.value);
      saveState();
    }
  });
  createProjectBtn.addEventListener("click", createNewProject);
  deleteProjectBtn.addEventListener("click", deleteCurrentProject);

  addMaterialBtn.addEventListener("click", addBaseMaterial);
  addPieceBtn.addEventListener("click", addProjectPiece);
  optimizeBtn.addEventListener("click", handleOptimize);

  projectNameEl.addEventListener("change", updateSettings);
  sawKerfEl.addEventListener("change", updateSettings);
  kerfUnitEl.addEventListener("change", updateSettings);

  prevSheetBtn.addEventListener("click", () => {
    if (currentSheetIndex > 0) {
      currentSheetIndex--;
      renderVisualization();
    }
  });
  nextSheetBtn.addEventListener("click", () => {
    if (currentSheetIndex < optimizedLayouts.length - 1) {
      currentSheetIndex++;
      renderVisualization();
    }
  });

  exportBtn.addEventListener("click", handleExport);
  printBtn.addEventListener("click", handlePrint);

  renderInventoryList();
  renderProjectPiecesList();
  renderProjectSelect();
  updateSheetNavigation();
  renderVisualization();
});
